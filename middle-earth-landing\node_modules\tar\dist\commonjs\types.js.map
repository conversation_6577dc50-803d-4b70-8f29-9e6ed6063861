{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";;;AAAO,MAAM,MAAM,GAAG,CAAC,CAAS,EAAsB,EAAE,CACtD,YAAI,CAAC,GAAG,CAAC,CAAkB,CAAC,CAAA;AADjB,QAAA,MAAM,UACW;AAEvB,MAAM,MAAM,GAAG,CAAC,CAAS,EAAsB,EAAE,CACtD,YAAI,CAAC,GAAG,CAAC,CAAkB,CAAC,CAAA;AADjB,QAAA,MAAM,UACW;AAiD9B,4CAA4C;AAC/B,QAAA,IAAI,GAAG,IAAI,GAAG,CAA+B;IACxD,CAAC,GAAG,EAAE,MAAM,CAAC;IACb,eAAe;IACf,CAAC,EAAE,EAAE,SAAS,CAAC;IACf,CAAC,GAAG,EAAE,MAAM,CAAC;IACb,CAAC,GAAG,EAAE,cAAc,CAAC;IACrB,2CAA2C;IAC3C,8CAA8C;IAC9C,CAAC,GAAG,EAAE,iBAAiB,CAAC;IACxB,CAAC,GAAG,EAAE,aAAa,CAAC;IACpB,CAAC,GAAG,EAAE,WAAW,CAAC;IAClB,CAAC,GAAG,EAAE,MAAM,CAAC;IACb,eAAe;IACf,CAAC,GAAG,EAAE,gBAAgB,CAAC;IACvB,cAAc;IACd,CAAC,GAAG,EAAE,sBAAsB,CAAC;IAC7B,CAAC,GAAG,EAAE,gBAAgB,CAAC;IACvB,wBAAwB;IACxB,OAAO;IACP,CAAC,GAAG,EAAE,YAAY,CAAC;IACnB,iDAAiD;IACjD,CAAC,GAAG,EAAE,YAAY,CAAC;IACnB,sBAAsB;IACtB,CAAC,GAAG,EAAE,OAAO,CAAC;IACd,gCAAgC;IAChC,CAAC,GAAG,EAAE,yBAAyB,CAAC;IAChC,2BAA2B;IAC3B,CAAC,GAAG,EAAE,qBAAqB,CAAC;IAC5B,OAAO;IACP,CAAC,GAAG,EAAE,kBAAkB,CAAC;IACzB,SAAS;IACT,CAAC,GAAG,EAAE,gBAAgB,CAAC;IACvB,OAAO;IACP,CAAC,GAAG,EAAE,YAAY,CAAC;IACnB,OAAO;IACP,CAAC,GAAG,EAAE,kBAAkB,CAAC;IACzB,SAAS;IACT,CAAC,GAAG,EAAE,mBAAmB,CAAC;CAC3B,CAAC,CAAA;AAEF,0BAA0B;AACb,QAAA,IAAI,GAAG,IAAI,GAAG,CACzB,KAAK,CAAC,IAAI,CAAC,YAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAC3C,CAAA", "sourcesContent": ["export const isCode = (c: string): c is EntryTypeCode =>\n  name.has(c as EntryTypeCode)\n\nexport const isName = (c: string): c is EntryTypeName =>\n  code.has(c as EntryTypeName)\n\nexport type EntryTypeCode =\n  | '0'\n  | ''\n  | '1'\n  | '2'\n  | '3'\n  | '4'\n  | '5'\n  | '6'\n  | '7'\n  | 'g'\n  | 'x'\n  | 'A'\n  | 'D'\n  | 'I'\n  | 'K'\n  | 'L'\n  | 'M'\n  | 'N'\n  | 'S'\n  | 'V'\n  | 'X'\n\nexport type EntryTypeName =\n  | 'File'\n  | 'OldFile'\n  | 'Link'\n  | 'SymbolicLink'\n  | 'CharacterDevice'\n  | 'BlockDevice'\n  | 'Directory'\n  | 'FIFO'\n  | 'ContiguousFile'\n  | 'GlobalExtendedHeader'\n  | 'ExtendedHeader'\n  | 'SolarisACL'\n  | 'GNUDumpDir'\n  | 'Inode'\n  | 'NextFileHasLongLinkpath'\n  | 'NextFileHasLongPath'\n  | 'ContinuationFile'\n  | 'OldGnuLongPath'\n  | 'SparseFile'\n  | 'TapeVolumeHeader'\n  | 'OldExtendedHeader'\n  | 'Unsupported'\n\n// map types from key to human-friendly name\nexport const name = new Map<EntryTypeCode, EntryTypeName>([\n  ['0', 'File'],\n  // same as File\n  ['', 'OldFile'],\n  ['1', 'Link'],\n  ['2', 'SymbolicLink'],\n  // Devices and FIFOs aren't fully supported\n  // they are parsed, but skipped when unpacking\n  ['3', 'CharacterDevice'],\n  ['4', 'BlockDevice'],\n  ['5', 'Directory'],\n  ['6', 'FIFO'],\n  // same as File\n  ['7', 'ContiguousFile'],\n  // pax headers\n  ['g', 'GlobalExtendedHeader'],\n  ['x', 'ExtendedHeader'],\n  // vendor-specific stuff\n  // skip\n  ['A', 'SolarisACL'],\n  // like 5, but with data, which should be skipped\n  ['D', 'GNUDumpDir'],\n  // metadata only, skip\n  ['I', 'Inode'],\n  // data = link path of next file\n  ['K', 'NextFileHasLongLinkpath'],\n  // data = path of next file\n  ['L', 'NextFileHasLongPath'],\n  // skip\n  ['M', 'ContinuationFile'],\n  // like L\n  ['N', 'OldGnuLongPath'],\n  // skip\n  ['S', 'SparseFile'],\n  // skip\n  ['V', 'TapeVolumeHeader'],\n  // like x\n  ['X', 'OldExtendedHeader'],\n])\n\n// map the other direction\nexport const code = new Map<EntryTypeName, EntryTypeCode>(\n  Array.from(name).map(kv => [kv[1], kv[0]]),\n)\n"]}